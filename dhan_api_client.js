// =========================================================
// DHAN API COMPREHENSIVE CODE EXAMPLES - NODE.JS/JAVASCRIPT
// =========================================================

const axios = require('axios');
const WebSocket = require('ws');

/**
 * PARAMETER OPTIONS REFERENCE
 * 
 * PRODUCT TYPES:
 * - "INTRADAY": Intraday positions (automatically squared off at end of day)
 * - "CNC": Cash and Carry (for delivery-based equity trading and investments)
 * - "MARGIN": Margin trading
 * - "MTF": Margin Trading Facility
 * - "CO": Cover Order - High leverage order with compulsory stop-loss
 * - "BO": Bracket Order - Allows placing main order with both target and stop-loss
 * 
 * VALIDITY TYPES:
 * - "DAY": Valid for the current trading day until market close
 * - "IOC": Immediate or Cancel - Executes immediately (fully/partially) or gets cancelled
 * 
 * ORDER TYPES:
 * - "MARKET": Execute at best available market price, no price required
 * - "LIMIT": Execute only at specified price or better
 * - "STOP_LOSS": Stop-Loss Limit - Triggers at stop price, then places a limit order
 * - "STOP_LOSS_MARKET": Stop-Loss Market - Triggers at stop price, then places a market order
 * 
 * TRANSACTION TYPES:
 * - "BUY": To buy/purchase securities
 * - "SELL": To sell securities
 * 
 * PRICE:
 * - For MARKET orders: Can be empty string
 * - For LIMIT orders: Specify the exact price at which you want to buy/sell
 * - For STOP_LOSS orders: The limit price at which order executes after trigger
 * 
 * TRIGGER PRICE:
 * - For MARKET/LIMIT orders: Can be empty string (not applicable)
 * - For STOP_LOSS/STOP_LOSS_MARKET orders: Price at which the stop-loss order is triggered
 * 
 * EXCHANGE SEGMENTS:
 * - "NSE_CM": NSE Cash Market (Equities)
 * - "NSE_FO": NSE Futures & Options
 * - "BSE_CM": BSE Cash Market (Equities)
 * - "BSE_FO": BSE Futures & Options
 * - "MCX_FO": MCX Futures & Options
 * 
 * ORDER STATUS:
 * - "TRANSIT": Order in transit
 * - "PENDING": Order pending at exchange
 * - "REJECTED": Order rejected
 * - "CANCELLED": Order cancelled
 * - "PART_TRADED": Order partially executed
 * - "TRADED": Order fully executed
 * - "EXPIRED": Order expired
 * 
 * LEG NAMES (for BO orders):
 * - "ENTRY_LEG": Entry leg of bracket order
 * - "TARGET_LEG": Target/profit leg of bracket order
 * - "STOP_LOSS_LEG": Stop loss leg of bracket order
 */

// Constants
const DHAN_API_URL = 'https://api.dhan.co';
const API_VERSION = 'v2';

// Initialize the Dhan client
function initializeDhanClient(accessToken) {
    return {
        axiosInstance: axios.create({
            baseURL: DHAN_API_URL,
            headers: {
                'Content-Type': 'application/json',
                'access-token': accessToken
            }
        }),
        accessToken: accessToken
    };
}

// =========================================================
// 📦 ORDER MANAGEMENT
// =========================================================

/**
 * Place a market order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} securityId - Security ID for the instrument
 * @param {number} quantity - Number of shares/units to trade
 * @param {string} transactionType - "BUY" or "SELL"
 * @param {string} exchangeSegment - Exchange segment like "NSE_CM", "NSE_FO", etc.
 * @param {string} productType - Product type: "INTRADAY", "CNC", etc.
 * @param {boolean} afterMarketOrder - Whether it's an after-market order (default: false)
 * @returns {Promise} - Order placement response
 */
function placeMarketOrder(accessToken, dhanClientId, securityId, quantity, transactionType, 
                         exchangeSegment, productType, afterMarketOrder = false) {
    const client = initializeDhanClient(accessToken);
    
    const orderData = {
        dhanClientId: dhanClientId,
        correlationId: `order-${Date.now()}`,
        transactionType: transactionType,
        exchangeSegment: exchangeSegment,
        productType: productType,
        orderType: "MARKET",
        validity: "DAY",
        securityId: securityId,
        quantity: quantity.toString(),
        disclosedQuantity: "",
        price: "",
        triggerPrice: "",
        afterMarketOrder: afterMarketOrder,
        amoTime: ""
    };

    return client.axiosInstance.post(`/${API_VERSION}/orders`, orderData)
        .then(response => {
            console.log('Market order placed successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error placing market order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Place a limit order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} securityId - Security ID for the instrument
 * @param {number} quantity - Number of shares/units to trade
 * @param {number} price - Specific price at which to execute the order
 * @param {string} transactionType - "BUY" or "SELL"
 * @param {string} exchangeSegment - Exchange segment like "NSE_CM", "NSE_FO", etc.
 * @param {string} productType - Product type: "INTRADAY", "CNC", etc.
 * @param {boolean} afterMarketOrder - Whether it's an after-market order (default: false)
 * @returns {Promise} - Order placement response
 */
function placeLimitOrder(accessToken, dhanClientId, securityId, quantity, price, transactionType, 
                        exchangeSegment, productType, afterMarketOrder = false) {
    const client = initializeDhanClient(accessToken);
    
    const orderData = {
        dhanClientId: dhanClientId,
        correlationId: `order-${Date.now()}`,
        transactionType: transactionType,
        exchangeSegment: exchangeSegment,
        productType: productType,
        orderType: "LIMIT",
        validity: "DAY",
        securityId: securityId,
        quantity: quantity.toString(),
        disclosedQuantity: "",
        price: price.toString(),
        triggerPrice: "",
        afterMarketOrder: afterMarketOrder,
        amoTime: ""
    };

    return client.axiosInstance.post(`/${API_VERSION}/orders`, orderData)
        .then(response => {
            console.log('Limit order placed successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error placing limit order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Place a stop-loss market order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} securityId - Security ID for the instrument
 * @param {number} quantity - Number of shares/units to trade
 * @param {number} triggerPrice - Price at which the stop-loss gets triggered
 * @param {string} transactionType - "BUY" or "SELL"
 * @param {string} exchangeSegment - Exchange segment like "NSE_CM", "NSE_FO", etc.
 * @param {string} productType - Product type: "INTRADAY", "CNC", etc.
 * @param {boolean} afterMarketOrder - Whether it's an after-market order (default: false)
 * @returns {Promise} - Order placement response
 */
function placeStopLossMarketOrder(accessToken, dhanClientId, securityId, quantity, triggerPrice, 
                                transactionType, exchangeSegment, productType, afterMarketOrder = false) {
    const client = initializeDhanClient(accessToken);
    
    const orderData = {
        dhanClientId: dhanClientId,
        correlationId: `order-${Date.now()}`,
        transactionType: transactionType,
        exchangeSegment: exchangeSegment,
        productType: productType,
        orderType: "STOP_LOSS_MARKET",
        validity: "DAY",
        securityId: securityId,
        quantity: quantity.toString(),
        disclosedQuantity: "",
        price: "",
        triggerPrice: triggerPrice.toString(),
        afterMarketOrder: afterMarketOrder,
        amoTime: ""
    };

    return client.axiosInstance.post(`/${API_VERSION}/orders`, orderData)
        .then(response => {
            console.log('Stop-loss market order placed successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error placing SL-M order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Place a stop-loss limit order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} securityId - Security ID for the instrument
 * @param {number} quantity - Number of shares/units to trade
 * @param {number} price - Limit price at which order executes after being triggered
 * @param {number} triggerPrice - Price at which the stop-loss gets triggered
 * @param {string} transactionType - "BUY" or "SELL"
 * @param {string} exchangeSegment - Exchange segment like "NSE_CM", "NSE_FO", etc.
 * @param {string} productType - Product type: "INTRADAY", "CNC", etc.
 * @param {boolean} afterMarketOrder - Whether it's an after-market order (default: false)
 * @returns {Promise} - Order placement response
 */
function placeStopLossLimitOrder(accessToken, dhanClientId, securityId, quantity, price, triggerPrice, 
                               transactionType, exchangeSegment, productType, afterMarketOrder = false) {
    const client = initializeDhanClient(accessToken);
    
    const orderData = {
        dhanClientId: dhanClientId,
        correlationId: `order-${Date.now()}`,
        transactionType: transactionType,
        exchangeSegment: exchangeSegment,
        productType: productType,
        orderType: "STOP_LOSS",
        validity: "DAY",
        securityId: securityId,
        quantity: quantity.toString(),
        disclosedQuantity: "",
        price: price.toString(),
        triggerPrice: triggerPrice.toString(),
        afterMarketOrder: afterMarketOrder,
        amoTime: ""
    };

    return client.axiosInstance.post(`/${API_VERSION}/orders`, orderData)
        .then(response => {
            console.log('Stop-loss limit order placed successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error placing SL order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Place a bracket order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} securityId - Security ID for the instrument
 * @param {number} quantity - Number of shares/units to trade
 * @param {number} price - Price for the main order
 * @param {number} boProfitValue - Target price value for the bracket order
 * @param {number} boStopLossValue - Stop loss price value for the bracket order
 * @param {string} transactionType - "BUY" or "SELL"
 * @param {string} exchangeSegment - Exchange segment like "NSE_CM", "NSE_FO", etc.
 * @param {boolean} afterMarketOrder - Whether it's an after-market order (default: false)
 * @returns {Promise} - Order placement response
 */
function placeBracketOrder(accessToken, dhanClientId, securityId, quantity, price, boProfitValue, 
                         boStopLossValue, transactionType, exchangeSegment, afterMarketOrder = false) {
    const client = initializeDhanClient(accessToken);
    
    const orderData = {
        dhanClientId: dhanClientId,
        correlationId: `order-${Date.now()}`,
        transactionType: transactionType,
        exchangeSegment: exchangeSegment,
        productType: "BO",
        orderType: "LIMIT",
        validity: "DAY",
        securityId: securityId,
        quantity: quantity.toString(),
        disclosedQuantity: "",
        price: price.toString(),
        triggerPrice: "",
        afterMarketOrder: afterMarketOrder,
        amoTime: "",
        boProfitValue: boProfitValue.toString(),
        boStopLossValue: boStopLossValue.toString()
    };

    return client.axiosInstance.post(`/${API_VERSION}/orders`, orderData)
        .then(response => {
            console.log('Bracket order placed successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error placing BO order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Modify an existing order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} orderId - The ID of the order to modify
 * @param {string} orderType - Order type "LIMIT", "MARKET", etc.
 * @param {string|null} quantity - New quantity (pass null to keep unchanged)
 * @param {string|null} price - New price (pass null to keep unchanged)
 * @param {string|null} disclosedQuantity - New disclosed quantity (pass null to keep unchanged)
 * @param {string|null} triggerPrice - New trigger price (for SL/SL-M orders, pass null to keep unchanged)
 * @param {string|null} validity - New validity (pass null to keep unchanged)
 * @param {string|null} legName - Leg name for BO/CO orders (pass null for regular orders)
 * @returns {Promise} - Order modification response
 */
function modifyOrder(accessToken, dhanClientId, orderId, orderType, quantity = null, price = null, 
                   disclosedQuantity = null, triggerPrice = null, validity = null, legName = null) {
    const client = initializeDhanClient(accessToken);
    
    const modificationData = {
        dhanClientId: dhanClientId,
        orderId: orderId,
        orderType: orderType
    };

    // Only add fields that need to be modified
    if (legName !== null) modificationData.legName = legName;
    if (quantity !== null) modificationData.quantity = quantity;
    if (price !== null) modificationData.price = price;
    if (disclosedQuantity !== null) modificationData.disclosedQuantity = disclosedQuantity;
    if (triggerPrice !== null) modificationData.triggerPrice = triggerPrice;
    if (validity !== null) modificationData.validity = validity;

    return client.axiosInstance.put(`/${API_VERSION}/orders/${orderId}`, modificationData)
        .then(response => {
            console.log('Order modified successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error modifying order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Cancel an order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} orderId - The ID of the order to cancel
 * @returns {Promise} - Order cancellation response
 */
function cancelOrder(accessToken, dhanClientId, orderId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.delete(`/${API_VERSION}/orders/${orderId}`, {
        data: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Order cancelled successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error cancelling order:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get details of a specific order
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} orderId - The ID of the order to fetch
 * @returns {Promise} - Order details
 */
function getOrderDetails(accessToken, dhanClientId, orderId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/orders/${orderId}`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Order details fetched successfully. Order data:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching order details:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get all orders for a user
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {Promise} - List of orders (orderbook)
 */
function getOrderBook(accessToken, dhanClientId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/orders`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Order book fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching order book:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get trade book (executed trades)
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {Promise} - List of trades
 */
function getTradeBook(accessToken, dhanClientId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/trades`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Trade book fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching trade book:', error.response ? error.response.data : error);
            throw error;
        });
}

// =========================================================
// 👤 USER & PORTFOLIO MANAGEMENT
// =========================================================

/**
 * Get user profile
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {Promise} - User profile data
 */
function getUserProfile(accessToken, dhanClientId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/user`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('User profile fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching user profile:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get account balance and funds details
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {Promise} - Fund details
 */
function getFundDetails(accessToken, dhanClientId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/fund`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Fund details fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching fund details:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get active positions
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {Promise} - Current positions
 */
function getPositions(accessToken, dhanClientId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/positions`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Positions fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching positions:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get holdings
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {Promise} - Holdings data
 */
function getHoldings(accessToken, dhanClientId) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/holdings`, {
        params: { dhanClientId: dhanClientId }
    })
        .then(response => {
            console.log('Holdings fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching holdings:', error.response ? error.response.data : error);
            throw error;
        });
}

// =========================================================
// 📊 MARKET DATA & INSTRUMENTS
// =========================================================

/**
 * Connect to Market Data WebSocket
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @returns {WebSocket} - WebSocket connection instance
 */
async function connectToMarketDataWebSocket(accessToken, dhanClientId) {
    const ws = new WebSocket(`wss://streams.dhan.co/${API_VERSION}/market-data`);
    
    return new Promise((resolve, reject) => {
        ws.on('open', function open() {
            console.log('WebSocket connection established');
            
            // Authenticate the WebSocket connection
            const authMessage = {
                action: 'auth',
                params: {
                    dhanClientId: dhanClientId,
                    accessToken: accessToken
                }
            };
            
            ws.send(JSON.stringify(authMessage));
        });
        
        ws.on('message', function incoming(data) {
            const message = JSON.parse(data);
            
            if (message.type === 'auth' && message.status === 'success') {
                console.log('WebSocket authenticated successfully');
                resolve(ws);
            } else if (message.type === 'auth' && message.status === 'error') {
                console.error('WebSocket authentication failed:', message);
                ws.close();
                reject(new Error('WebSocket authentication failed'));
            } else {
                console.log('WebSocket message:', message);
            }
        });
        
        ws.on('error', function error(err) {
            console.error('WebSocket error:', err);
            reject(err);
        });
        
        ws.on('close', function close() {
            console.log('WebSocket connection closed');
        });
    });
}

/**
 * Subscribe to market data for specific instruments
 * 
 * @param {WebSocket} ws - WebSocket connection instance
 * @param {Array<string>} securityIds - Array of security IDs to subscribe to
 * @param {string} mode - Subscription mode: "full", "quote", "depth" (default: "full")
 * @returns {void}
 */
function subscribeToMarketData(ws, securityIds, mode = "full") {
    const subscribeMessage = {
        action: 'subscribe',
        params: {
            mode: mode,
            securityIds: securityIds
        }
    };
    
    ws.send(JSON.stringify(subscribeMessage));
    console.log(`Subscribed to ${securityIds.length} instruments in ${mode} mode`);
}

/**
 * Unsubscribe from market data for specific instruments
 * 
 * @param {WebSocket} ws - WebSocket connection instance
 * @param {Array<string>} securityIds - Array of security IDs to unsubscribe from
 * @returns {void}
 */
function unsubscribeFromMarketData(ws, securityIds) {
    const unsubscribeMessage = {
        action: 'unsubscribe',
        params: {
            securityIds: securityIds
        }
    };
    
    ws.send(JSON.stringify(unsubscribeMessage));
    console.log(`Unsubscribed from ${securityIds.length} instruments`);
}

/**
 * Get market quotes for specific instruments
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {Array<string>} securityIds - Array of security IDs
 * @returns {Promise} - Market quotes
 */
function getMarketQuotes(accessToken, dhanClientId, securityIds) {
    const client = initializeDhanClient(accessToken);
    
    return client.axiosInstance.get(`/${API_VERSION}/quotes`, {
        params: { 
            dhanClientId: dhanClientId,
            securityIds: securityIds.join(',')
        }
    })
        .then(response => {
            console.log('Market quotes fetched successfully:', response.data);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching market quotes:', error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get historical data for an instrument
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} securityId - Security ID for the instrument
 * @param {string} exchangeSegment - Exchange segment like "NSE_EQ", "NSE_FO", etc.
 * @param {string} instrument - Instrument type (e.g., "EQUITY")
 * @param {string} fromDate - Start date in YYYY-MM-DD format for daily or YYYY-MM-DD HH:MM:SS for intraday
 * @param {string} toDate - End date in YYYY-MM-DD format for daily or YYYY-MM-DD HH:MM:SS for intraday
 * @param {string} [interval] - Time interval for intraday data (e.g., "1", "5", "15", "25", "60")
 * @param {boolean} [oi=false] - Whether to include Open Interest data (for F&O)
 * @param {number} [expiryCode] - Expiry code for derivatives
 * @returns {Promise} - Historical data
 */
function getHistoricalData(accessToken, securityId, exchangeSegment, instrument, fromDate, toDate, interval, oi = false, expiryCode) {
    const client = initializeDhanClient(accessToken);
    
    // Determine if we're fetching daily or intraday data based on interval
    const isIntraday = interval !== undefined;
    const endpoint = isIntraday ? `/${API_VERSION}/charts/intraday` : `/${API_VERSION}/charts/historical`;
    
    // Prepare request data according to API documentation
    const requestData = {
        securityId: securityId,
        exchangeSegment: exchangeSegment,
        instrument: instrument,
        fromDate: fromDate,
        toDate: toDate,
        oi: oi
    };
    
    // Add interval for intraday requests
    if (isIntraday) {
        requestData.interval = interval;
    }
    
    // Add expiryCode for derivatives if provided
    if (expiryCode !== undefined) {
        requestData.expiryCode = expiryCode;
    }
    
    // Make the API request
    return client.axiosInstance.post(endpoint, requestData)
        .then(response => {
            console.log(`${isIntraday ? 'Intraday' : 'Daily'} historical data fetched successfully`);
            return response.data;
        })
        .catch(error => {
            console.error(`Error fetching ${isIntraday ? 'intraday' : 'daily'} historical data:`, 
                         error.response ? error.response.data : error);
            throw error;
        });
}

/**
 * Get all available instruments
 * 
 * @param {string} accessToken - Authentication token
 * @param {string} dhanClientId - User specific ID generated by Dhan
 * @param {string} [exchangeSegment] - Specific exchange segment to filter by (optional)
 * @returns {Promise} - List of instruments
 */
function getInstruments(accessToken, dhanClientId, exchangeSegment = null) {
    const client = initializeDhanClient(accessToken);
    
    const params = { dhanClientId: dhanClientId };
    if (exchangeSegment) {
        params.exchangeSegment = exchangeSegment;
    }
    
    return client.axiosInstance.get(`/${API_VERSION}/instruments`, { params })
        .then(response => {
            console.log(`Instruments fetched successfully. Count: ${response.data.length}`);
            return response.data;
        })
        .catch(error => {
            console.error('Error fetching instruments:', error.response ? error.response.data : error);
            throw error;
        });
}

// Export all functions for external use
module.exports = {
    // Order Management
    placeMarketOrder,
    placeLimitOrder,
    placeStopLossMarketOrder,
    placeStopLossLimitOrder,
    placeBracketOrder,
    modifyOrder,
    cancelOrder,
    getOrderDetails,
    getOrderBook,
    getTradeBook,
    
    // User & Portfolio Management
    getUserProfile,
    getFundDetails,
    getPositions,
    getHoldings,
    
    // Market Data & Instruments
    connectToMarketDataWebSocket,
    subscribeToMarketData,
    unsubscribeFromMarketData,
    getMarketQuotes,
    getHistoricalData,
    getInstruments
}; 