import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const { email } = await request.json()
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()
    
    // Get current user session to check if they're authenticated
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user || user.email !== email) {
      // For security, don't reveal email verification status for other users
      return NextResponse.json({ verified: false })
    }

    // Check if email is verified using Supabase's built-in email_confirmed_at field
    const isVerified = user.email_confirmed_at !== null

    return NextResponse.json({ verified: isVerified })
  } catch (error) {
    console.error('Error checking email verification:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 