# Test info

- Name: Website Content >> should have proper content on pricing page
- Location: C:\Users\<USER>\Desktop\kk\tests\content.spec.ts:19:7

# Error details

```
Error: expect(received).toBeGreaterThanOrEqual(expected)

Expected: >= 1
Received:    0
    at C:\Users\<USER>\Desktop\kk\tests\content.spec.ts:32:24
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Website Content', () => {
   4 |   test('should have proper content on home page', async ({ page }) => {
   5 |     await page.goto('/');
   6 |     
   7 |     // Check for main sections - less strict checks
   8 |     await expect(page.getByRole('heading', { level: 1 }).first()).toBeVisible();
   9 |     
   10 |     // Look for a specific heading by name
   11 |     const headings = page.getByRole('heading');
   12 |     expect(await headings.count()).toBeGreaterThanOrEqual(1);
   13 |     
   14 |     // Check for sections
   15 |     const sectionCount = await page.locator('section').count();
   16 |     expect(sectionCount).toBeGreaterThanOrEqual(1);
   17 |   });
   18 |
   19 |   test('should have proper content on pricing page', async ({ page }) => {
   20 |     await page.goto('/pricing');
   21 |     
   22 |     // Check if we're on the pricing page or redirected to login
   23 |     if (page.url().includes('/auth/login')) {
   24 |       // We're on login page, just verify the URL
   25 |       expect(page.url()).toContain('/auth/login');
   26 |     } else {
   27 |       // We're on pricing page
   28 |       expect(page.url()).toContain('/pricing');
   29 |       
   30 |       // Look for pricing elements without being too specific
   31 |       const sections = await page.locator('section').count();
>  32 |       expect(sections).toBeGreaterThanOrEqual(1);
      |                        ^ Error: expect(received).toBeGreaterThanOrEqual(expected)
   33 |     }
   34 |   });
   35 |
   36 |   test('should have proper content on marketplace page', async ({ page }) => {
   37 |     await page.goto('/marketplace');
   38 |     
   39 |     // Check if we're on the marketplace page or redirected to login
   40 |     if (page.url().includes('/auth/login')) {
   41 |       // We're on login page, just verify the URL
   42 |       expect(page.url()).toContain('/auth/login');
   43 |     } else {
   44 |       // We're on marketplace page
   45 |       expect(page.url()).toContain('/marketplace');
   46 |       
   47 |       // Look for marketplace elements without being too specific
   48 |       const sections = await page.locator('section').count();
   49 |       expect(sections).toBeGreaterThanOrEqual(1);
   50 |     }
   51 |   });
   52 |
   53 |   test('should have proper content on support page', async ({ page }) => {
   54 |     await page.goto('/support');
   55 |     
   56 |     // Check if we're on the support page or redirected to login
   57 |     if (page.url().includes('/auth/login')) {
   58 |       // We're on login page, just verify the URL
   59 |       expect(page.url()).toContain('/auth/login');
   60 |     } else {
   61 |       // We're on support page
   62 |       expect(page.url()).toContain('/support');
   63 |       
   64 |       // Look for sections on the page
   65 |       const sections = await page.locator('section').count();
   66 |       expect(sections).toBeGreaterThanOrEqual(1);
   67 |     }
   68 |   });
   69 |
   70 |   test('should have proper content on auth pages', async ({ page }) => {
   71 |     // Login page
   72 |     await page.goto('/auth/login');
   73 |     
   74 |     // Check if we're on the login page
   75 |     expect(page.url()).toContain('/auth/login');
   76 |     
   77 |     // Look for basic elements that should be on a login page
   78 |     const form = page.locator('form');
   79 |     if (await form.count() > 0) {
   80 |       await expect(form.first()).toBeVisible();
   81 |     }
   82 |   });
   83 |
   84 |   test('should have proper content on legal pages', async ({ page }) => {
   85 |     // Privacy Policy
   86 |     await page.goto('/privacy-policy');
   87 |     
   88 |     // Check if we're on the privacy policy page or redirected to login
   89 |     if (page.url().includes('/auth/login')) {
   90 |       // We're on login page, just verify the URL
   91 |       expect(page.url()).toContain('/auth/login');
   92 |     } else {
   93 |       // We're on privacy policy page
   94 |       expect(page.url()).toContain('/privacy-policy');
   95 |       
   96 |       // Look for content on the page
   97 |       const paragraphs = await page.locator('p').count();
   98 |       expect(paragraphs).toBeGreaterThanOrEqual(1);
   99 |     }
  100 |     
  101 |     // Terms & Conditions
  102 |     await page.goto('/terms-conditions');
  103 |     
  104 |     // Check if we're on the terms page or redirected to login
  105 |     if (page.url().includes('/auth/login')) {
  106 |       // We're on login page, just verify the URL
  107 |       expect(page.url()).toContain('/auth/login');
  108 |     } else {
  109 |       // We're on terms page
  110 |       expect(page.url()).toContain('/terms-conditions');
  111 |       
  112 |       // Look for content on the page
  113 |       const paragraphs = await page.locator('p').count();
  114 |       expect(paragraphs).toBeGreaterThanOrEqual(1);
  115 |     }
  116 |   });
  117 | }); 
```