# Test info

- Name: User Signup Feature >> Page Load and UI Elements >> should load signup page with all required elements
- Location: C:\Users\<USER>\Code\Z_build\tests\signup.spec.ts:10:9

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: getByRole('heading', { name: /sign up/i })
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for getByRole('heading', { name: /sign up/i })

    at C:\Users\<USER>\Code\Z_build\tests\signup.spec.ts:15:69
```

# Page snapshot

```yaml
- text: "System: Sign Up Sign up Create a new account Email"
- textbox "Email"
- text: Password
- textbox "Password"
- text: Repeat Password
- textbox "Repeat Password"
- button "Sign up"
- text: Already have an account?
- link "Login":
  - /url: /auth/login
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('User Signup Feature', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to signup page before each test
   6 |     await page.goto('/auth/sign-up');
   7 |   });
   8 |
   9 |   test.describe('Page Load and UI Elements', () => {
   10 |     test('should load signup page with all required elements', async ({ page }) => {
   11 |       // Check page title and URL
   12 |       await expect(page).toHaveURL(/.*\/auth\/sign-up/);
   13 |       
   14 |       // Check for main heading
>  15 |       await expect(page.getByRole('heading', { name: /sign up/i })).toBeVisible();
      |                                                                     ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   16 |       
   17 |       // Check for form elements
   18 |       await expect(page.getByLabel(/email/i)).toBeVisible();
   19 |       await expect(page.getByLabel(/^password$/i)).toBeVisible();
   20 |       await expect(page.getByLabel(/repeat password/i)).toBeVisible();
   21 |       
   22 |       // Check for submit button
   23 |       await expect(page.getByRole('button', { name: /sign up/i })).toBeVisible();
   24 |       
   25 |       // Check for link to login page
   26 |       await expect(page.getByRole('link', { name: /sign in/i })).toBeVisible();
   27 |     });
   28 |
   29 |     test('should have proper form field attributes', async ({ page }) => {
   30 |       const emailField = page.getByLabel(/email/i);
   31 |       const passwordField = page.getByLabel(/^password$/i);
   32 |       const repeatPasswordField = page.getByLabel(/repeat password/i);
   33 |       
   34 |       // Check email field attributes
   35 |       await expect(emailField).toHaveAttribute('type', 'email');
   36 |       await expect(emailField).toHaveAttribute('required');
   37 |       
   38 |       // Check password field attributes
   39 |       await expect(passwordField).toHaveAttribute('type', 'password');
   40 |       await expect(passwordField).toHaveAttribute('required');
   41 |       
   42 |       // Check repeat password field attributes
   43 |       await expect(repeatPasswordField).toHaveAttribute('type', 'password');
   44 |       await expect(repeatPasswordField).toHaveAttribute('required');
   45 |     });
   46 |   });
   47 |
   48 |   test.describe('Form Validation - Required Fields', () => {
   49 |     test('should show validation for empty email field', async ({ page }) => {
   50 |       const submitButton = page.getByRole('button', { name: /sign up/i });
   51 |       
   52 |       // Try to submit with empty email
   53 |       await page.getByLabel(/^password$/i).fill('password123');
   54 |       await page.getByLabel(/repeat password/i).fill('password123');
   55 |       await submitButton.click();
   56 |       
   57 |       // Check that form doesn't submit (still on signup page)
   58 |       await expect(page).toHaveURL(/.*\/auth\/sign-up/);
   59 |     });
   60 |
   61 |     test('should show validation for empty password field', async ({ page }) => {
   62 |       const submitButton = page.getByRole('button', { name: /sign up/i });
   63 |       
   64 |       // Try to submit with empty password
   65 |       await page.getByLabel(/email/i).fill('<EMAIL>');
   66 |       await page.getByLabel(/repeat password/i).fill('password123');
   67 |       await submitButton.click();
   68 |       
   69 |       // Check that form doesn't submit (still on signup page)
   70 |       await expect(page).toHaveURL(/.*\/auth\/sign-up/);
   71 |     });
   72 |
   73 |     test('should show validation for empty repeat password field', async ({ page }) => {
   74 |       const submitButton = page.getByRole('button', { name: /sign up/i });
   75 |       
   76 |       // Try to submit with empty repeat password
   77 |       await page.getByLabel(/email/i).fill('<EMAIL>');
   78 |       await page.getByLabel(/^password$/i).fill('password123');
   79 |       await submitButton.click();
   80 |       
   81 |       // Check that form doesn't submit (still on signup page)
   82 |       await expect(page).toHaveURL(/.*\/auth\/sign-up/);
   83 |     });
   84 |   });
   85 |
   86 |   test.describe('Form Validation - Email Format', () => {
   87 |     test('should validate email format', async ({ page }) => {
   88 |       const emailField = page.getByLabel(/email/i);
   89 |       const submitButton = page.getByRole('button', { name: /sign up/i });
   90 |       
   91 |       // Test invalid email formats
   92 |       const invalidEmails = ['invalid-email', 'test@', '@example.com', 'test.example.com'];
   93 |       
   94 |       for (const invalidEmail of invalidEmails) {
   95 |         await emailField.fill(invalidEmail);
   96 |         await page.getByLabel(/^password$/i).fill('password123');
   97 |         await page.getByLabel(/repeat password/i).fill('password123');
   98 |         await submitButton.click();
   99 |         
  100 |         // Should still be on signup page due to validation
  101 |         await expect(page).toHaveURL(/.*\/auth\/sign-up/);
  102 |         
  103 |         // Clear the field for next iteration
  104 |         await emailField.clear();
  105 |       }
  106 |     });
  107 |   });
  108 |
  109 |   test.describe('Password Validation', () => {
  110 |     test('should show error for password less than 8 characters', async ({ page }) => {
  111 |       await page.getByLabel(/email/i).fill('<EMAIL>');
  112 |       await page.getByLabel(/^password$/i).fill('short');
  113 |       await page.getByLabel(/repeat password/i).fill('short');
  114 |       
  115 |       await page.getByRole('button', { name: /sign up/i }).click();
```