'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { CheckCircle } from 'lucide-react'
import { useNotification } from '@/lib/notification'

export default function EmailVerifiedPage() {
  const router = useRouter()
  const { showNotification } = useNotification()

  useEffect(() => {
    showNotification({
      title: 'EMAIL_VERIFIED',
      description: 'Your email has been successfully verified. You can now sign in to your account.',
      type: 'success',
      duration: 5000
    })
  }, [showNotification])

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-black text-white font-mono relative">
      <div className="w-full max-w-md">
        <div className="mb-8 flex items-center justify-center gap-2">
          <span className="text-xs tracking-widest uppercase text-zinc-400">System: Email Verified</span>
        </div>
        <div className="bg-zinc-950 border border-zinc-900 rounded-xl shadow-md p-8 text-center">
          <div className="flex justify-center mb-6">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <h1 className="text-2xl font-bold mb-4 text-green-400">Email Verified Successfully</h1>
          <p className="text-zinc-300 mb-6">
            Your email address has been confirmed. You can now access all features of your account.
          </p>
          <div className="space-y-3">
            <Button 
              asChild 
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <Link href="/auth/login">
                Continue to Login
              </Link>
            </Button>
            <Button 
              asChild 
              variant="outline" 
              className="w-full border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              <Link href="/dashboard">
                Go to Dashboard
              </Link>
            </Button>
          </div>
        </div>
      </div>
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#111_1px,transparent_1px),linear-gradient(to_bottom,#111_1px,transparent_1px)] bg-[size:32px_32px] opacity-20 pointer-events-none -z-10" />
    </div>
  )
} 