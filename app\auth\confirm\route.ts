import { createClient } from '@/lib/supabase/server'
import { type EmailOtpType } from '@supabase/supabase-js'
import { redirect } from 'next/navigation'
import { type NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/email-verified'

  if (token_hash && type) {
    const supabase = await createClient()

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })
    
    if (!error) {
      // Successfully verified email - redirect to success page
      redirect('/email-verified')
    } else {
      // Handle different types of verification errors
      let errorMessage = 'Invalid or expired verification link'
      
      if (error.message.includes('expired')) {
        errorMessage = 'Verification link has expired'
      } else if (error.message.includes('invalid')) {
        errorMessage = 'Invalid verification link'
      }
      
      // redirect the user to an error page with the specific error message
      redirect(`/auth/error?error=${encodeURIComponent(errorMessage)}`)
    }
  }

  // redirect the user to an error page when no token or type is provided
  redirect(`/auth/error?error=${encodeURIComponent('No verification token provided')}`)
}
