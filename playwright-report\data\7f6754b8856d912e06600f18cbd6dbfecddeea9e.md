# Test info

- Name: Website Navigation >> should navigate to main pages
- Location: C:\Users\<USER>\Desktop\kk\tests\navigation.spec.ts:4:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: getByR<PERSON>('link', { name: /dashboard/i })
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for getB<PERSON><PERSON><PERSON>('link', { name: /dashboard/i })

    at C:\Users\<USER>\Desktop\kk\tests\navigation.spec.ts:11:66
```

# Page snapshot

```yaml
- banner:
  - navigation:
    - link "home":
      - /url: /
      - img
      - text: ALGOZ
    - list:
      - listitem:
        - link "FEATURES":
          - /url: /#features
      - listitem:
        - link "SOLUTION":
          - /url: /#solution
      - listitem:
        - link "PRICING":
          - /url: /#pricing
      - listitem:
        - link "ABOUT":
          - /url: /#about
    - link "LOGIN":
      - /url: /auth
- main:
  - link "ALGORITHMIC_TRADING_INTERFACE":
    - /url: "#link"
    - text: ALGORITHMIC_TRADING_INTERFACE
    - img
    - img
  - 'heading "ALGOZ: TRADING_SMARTER" [level=1]'
  - paragraph: HARNESS THE POWER OF ALGORITHMIC TRADING WITH OUR INTELLIGENT PLATFORM. OPTIMIZE YOUR STRATEGIES, MINIMIZE EMOTIONAL DECISIONS, AND MAXIMIZE YOUR RETURNS.
  - link "START_TRADING":
    - /url: "#start-trading"
  - link "LEARN_MORE":
    - /url: "#link"
  - img
  - paragraph: TRUSTED_PARTNERS
  - text: ALICEBLUE ANGLEBROKING DHANHQ ZERODHA FYERS UPSTOX BINANCE DELTAEXCHANGE KOTAK FLATTRADE ICICI_DIRECT IIFL ALICEBLUE ANGLEBROKING DHANHQ ZERODHA FYERS UPSTOX BINANCE DELTAEXCHANGE KOTAK FLATTRADE ICICI_DIRECT IIFL
  - img
  - heading "ADVANCED_FEATURES" [level=2]
  - paragraph: ALGORITHMIC_TRADING_SOLUTIONS_FOR_MODERN_TRADERS
  - heading "TRADINGVIEW" [level=3]
  - heading "SCALPING_TOOL" [level=3]
  - heading "COPY_TRADING" [level=3]
  - heading "MARKETPLACE" [level=3]
  - heading "CUSTOM_DEVELOPER" [level=3]
  - heading "OPTIMIZATION" [level=3]
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - img
  - heading "Integration Partners" [level=2]
  - paragraph: Connect AlgoZ seamlessly with your favorite trading platforms and financial services.
  - link "Explore Integrations":
    - /url: "#"
  - img
  - heading "USER FEEDBACK" [level=2]
  - paragraph: REAL TRADERS SHARE THEIR EXPERIENCE WITH OUR ALGORITHMIC TRADING PLATFORM
  - blockquote:
    - paragraph: "\"ALGOZ HAS TRANSFORMED MY TRADING APPROACH. THEIR ADVANCED ALGORITHMS IMPROVED MY PERFORMANCE.\""
    - img "Rajesh Sharma"
    - text: RAJESH SHARMA PROFESSIONAL TRADER
  - blockquote:
    - paragraph: "\"THE BACKTESTING CAPABILITIES ARE UNMATCHED. TEST STRATEGIES IN MINUTES.\""
    - img "Priya Patel"
    - text: PRIYA PATEL HEDGE FUND ANALYST
  - blockquote:
    - paragraph: "\"ALGOZ ML MODELS IDENTIFY PATTERNS I WOULD MISS.\""
    - img "Vikram Mehta"
    - text: VIKRAM MEHTA DATA SCIENTIST
  - blockquote:
    - paragraph: "\"AS A NOVICE TRADER, ALGOZ INTERFACE IS INVALUABLE.\""
    - img "Anjali Gupta"
    - paragraph: ANJALI GUPTA
    - text: RETAIL INVESTOR
  - heading "PRICING_PLAN" [level=2]
  - paragraph: CHOOSE_THE_PLAN_THAT_BEST_FITS_YOUR_TRADING_NEEDS
  - text: "1000"
  - img
  - text: ₹999
  - button "Buy Now"
  - text: "2500"
  - img
  - text: ₹2249
  - button "Buy Now"
  - text: "500"
  - img
  - text: ₹
  - spinbutton: "500"
  - paragraph: ₹1 = 1 Coin
  - button "Buy Now"
- contentinfo:
  - img
  - heading "ALGOZ" [level=2]
  - textbox "ENTER_YOUR_EMAIL"
  - button "Subscribe":
    - img
    - text: Subscribe
  - heading "PAGES" [level=3]
  - navigation:
    - link "PRIVACY_POLICY":
      - /url: /privacy-policy
    - link "DISCLAIMER_POLICY":
      - /url: /disclaimer-policy
    - link "TERMS_AND_CONDITIONS":
      - /url: /terms-conditions
    - link "COOKIES_POLICY":
      - /url: /cookies-policy
    - link "REFUND_AND_CANCELLATION":
      - /url: /refund-policy
  - heading "CONNECT_WITH_US" [level=3]
  - navigation:
    - link "WHATSAPP_SUPPORT":
      - /url: https://wa.me/919241740350
      - img
      - text: WHATSAPP_SUPPORT
    - link "TELEGRAM_SUPPORT":
      - /url: https://t.me/AlgoZsupport1
      - img
      - text: TELEGRAM_SUPPORT
    - link "+91 9241740350":
      - /url: tel:+919241740350
      - img
      - text: +91 9241740350
  - paragraph: © 2024 ALGOZ_TRADING. ALL_RIGHTS_RESERVED.
  - navigation:
    - link "PRIVACY_POLICY":
      - /url: /privacy-policy
    - link "TERMS_OF_SERVICE":
      - /url: /terms-conditions
    - link "COOKIE_SETTINGS":
      - /url: /cookies-policy
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Website Navigation', () => {
   4 |   test('should navigate to main pages', async ({ page }) => {
   5 |     // Home page
   6 |     await page.goto('/');
   7 |     await expect(page).toHaveTitle(/AlgoZ/);
   8 |     
   9 |     // Check main navigation links are present
  10 |     await expect(page.getByRole('link', { name: /pricing/i })).toBeVisible();
> 11 |     await expect(page.getByRole('link', { name: /dashboard/i })).toBeVisible();
     |                                                                  ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  12 |     await expect(page.getByRole('link', { name: /marketplace/i })).toBeVisible();
  13 |     
  14 |     // Navigate to Pricing page
  15 |     await page.getByRole('link', { name: /pricing/i }).click();
  16 |     await expect(page.url()).toContain('/pricing');
  17 |     
  18 |     // Navigate to Marketplace page
  19 |     await page.goto('/');
  20 |     await page.getByRole('link', { name: /marketplace/i }).click();
  21 |     await expect(page.url()).toContain('/marketplace');
  22 |     
  23 |     // Navigate to Support page
  24 |     await page.goto('/support');
  25 |     await expect(page.url()).toContain('/support');
  26 |     
  27 |     // Navigate to Auth page
  28 |     await page.goto('/auth');
  29 |     await expect(page.url()).toContain('/auth');
  30 |     
  31 |     // Navigate to Login page
  32 |     await page.goto('/auth/login');
  33 |     await expect(page.url()).toContain('/auth/login');
  34 |     
  35 |     // Check footer links (if they exist)
  36 |     await page.goto('/');
  37 |     // Try to find privacy policy link, but don't fail if not found
  38 |     const privacyLink = page.getByRole('link', { name: /privacy policy/i });
  39 |     if (await privacyLink.isVisible()) {
  40 |       await privacyLink.click();
  41 |       await expect(page.url()).toContain('/privacy-policy');
  42 |     }
  43 |     
  44 |     // Navigate to Terms & Conditions page
  45 |     await page.goto('/terms-conditions');
  46 |     await expect(page.url()).toContain('/terms-conditions');
  47 |   });
  48 |
  49 |   test('should check dashboard features', async ({ page }) => {
  50 |     // Go to dashboard (this might redirect to login if auth is required)
  51 |     await page.goto('/dashboard');
  52 |     
  53 |     // Check if we're on dashboard or login page
  54 |     if (page.url().includes('/auth/login')) {
  55 |       // We're redirected to login, just verify we're on login page
  56 |       expect(page.url()).toContain('/auth/login');
  57 |     } else {
  58 |       // We're on the dashboard, let's check some elements
  59 |       await expect(page.url()).toContain('/dashboard');
  60 |     }
  61 |   });
  62 |
  63 |   test('should check responsive design', async ({ page }) => {
  64 |     // Test on mobile viewport
  65 |     await page.setViewportSize({ width: 390, height: 844 });
  66 |     await page.goto('/');
  67 |     
  68 |     // Look for mobile elements like a menu button or hamburger icon
  69 |     // This is more permissive as different designs have different mobile UI
  70 |     const mobileMenuButton = page.getByRole('button', { name: /menu/i })
  71 |                             .or(page.locator('button').filter({ hasText: '' })
  72 |                             .or(page.locator('[aria-label="Toggle menu"]')));
  73 |     
  74 |     // If the mobile menu button exists, test it
  75 |     if (await mobileMenuButton.count() > 0) {
  76 |       await expect(mobileMenuButton.first()).toBeVisible();
  77 |     }
  78 |     
  79 |     // Test on tablet viewport
  80 |     await page.setViewportSize({ width: 768, height: 1024 });
  81 |     await page.goto('/');
  82 |     
  83 |     // Test on desktop viewport
  84 |     await page.setViewportSize({ width: 1440, height: 900 });
  85 |     await page.goto('/');
  86 |   });
  87 | }); 
```