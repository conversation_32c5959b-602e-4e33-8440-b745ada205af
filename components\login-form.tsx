'use client'

import { cn } from '@/lib/utils'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useNotification } from '@/lib/notification'

export function LoginForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { showNotification } = useNotification()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    const supabase = createClient()
    setIsLoading(true)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      if (error) throw error
      
      // Check if email is verified
      if (data.user && !data.user.email_confirmed_at) {
        // Sign out the user since email is not verified
        await supabase.auth.signOut()
        
        showNotification({
          title: 'EMAIL_NOT_VERIFIED',
          description: 'Please verify your email before logging in. Check your inbox for the verification link.',
          type: 'warning',
          duration: 7000
        })
        setIsLoading(false)
        return
      }
      
      // Show success notification
      showNotification({
        title: 'ACCESS_GRANTED',
        description: 'Authentication successful. Welcome back to the platform.',
        type: 'success',
        duration: 3000
      })
      
      // Wait a moment before redirecting
      setTimeout(() => {
        router.push('/dashboard')
      }, 1500)
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      
      // Show error notification
      if (errorMessage.toLowerCase().includes('invalid') || 
          errorMessage.toLowerCase().includes('credentials')) {
        showNotification({
          title: 'ACCESS_DENIED',
          description: 'The provided credentials do not match our records. Please check your email and password.',
          type: 'error',
          duration: 5000
        })
      } else if (errorMessage.toLowerCase().includes('too many requests')) {
        showNotification({
          title: 'RATE_LIMIT_EXCEEDED',
          description: 'Too many login attempts. Please wait a moment before trying again.',
          type: 'warning',
          duration: 7000
        })
      } else if (errorMessage.toLowerCase().includes('email not confirmed')) {
        showNotification({
          title: 'EMAIL_NOT_VERIFIED',
          description: 'Please verify your email before logging in. Check your inbox for the verification link.',
          type: 'warning',
          duration: 7000
        })
      } else {
        showNotification({
          title: 'SYSTEM_ERROR',
          description: 'An unexpected error occurred during authentication. Please try again later.',
          type: 'error',
          duration: 5000
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendVerification = async () => {
    if (!email) {
      showNotification({
        title: 'EMAIL_REQUIRED',
        description: 'Please enter your email address first.',
        type: 'warning',
        duration: 3000
      })
      return
    }

    try {
      const supabase = createClient()
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/email-verified`
        }
      })

      if (error) throw error

      showNotification({
        title: 'VERIFICATION_SENT',
        description: 'Verification email sent. Please check your inbox.',
        type: 'success',
        duration: 5000
      })
    } catch (error) {
      showNotification({
        title: 'RESEND_ERROR',
        description: 'Failed to resend verification email. Please try again later.',
        type: 'error',
        duration: 5000
      })
    }
  }

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>Enter your email below to login to your account</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Logging in...' : 'Login'}
              </Button>
              
              {/* Resend verification button */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={handleResendVerification}
                  className="text-sm text-zinc-400 hover:text-zinc-300 underline-offset-4 hover:underline"
                >
                  Didn't receive verification email? Resend
                </button>
              </div>
            </div>
            <div className="mt-4 text-center text-sm">
              Don&apos;t have an account?{' '}
              <Link href="/auth/sign-up" className="underline underline-offset-4">
                Sign up
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
