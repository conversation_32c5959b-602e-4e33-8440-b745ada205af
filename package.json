{"name": "next-auth-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/postcss": "^4.1.4", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotted-map": "^2.2.3", "framer-motion": "^12.7.4", "fyers-api-v3": "^1.4.1", "glob": "^10.3.10", "lightweight-charts": "^5.0.6", "lru-cache": "^10.2.0", "lucide-react": "^0.488.0", "motion": "^12.7.4", "next": "latest", "next-themes": "^0.4.6", "react": "latest", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.7", "react-dom": "latest", "react-dropzone": "^14.3.8", "react-intersection-observer": "^9.16.0", "react-use-measure": "^2.1.7", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0"}, "devDependencies": {"@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.5", "@types/node": "latest", "@types/react": "latest", "@types/react-dom": "latest", "autoprefixer": "latest", "babel-jest": "^29.7.0", "eslint": "latest", "eslint-config-next": "latest", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "latest", "tailwindcss": "^4", "typescript": "latest"}}